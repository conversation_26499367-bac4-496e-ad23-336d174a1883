{"advanced_ui_import": false, "auto_stash_active_action": true, "blender": {"export_method": {"abc": {"manual_transform": {"global_scale": 100.0}, "object_options": {"normals": true, "orcos": true, "packuv": true, "uvs": true, "vcolors": false}, "scene_options": {"export_custom_properties": true, "use_instancing": true}}, "fbx": {"animation": {"bake_anim": true, "bake_anim_force_startend_keying": true, "bake_anim_simplify_factor": 0.0, "bake_anim_step": 1.0, "bake_anim_use_all_bones": true}, "armature": {"add_leaf_bones": false, "armature_nodetype": "NULL", "primary_bone_axis": "Y", "secondary_bone_axis": "X", "use_armature_deform_only": false}, "extras": {"use_metadata": true}, "geometry": {"mesh_smooth_type": "FACE", "use_mesh_edges": false, "use_mesh_modifiers": true, "use_mesh_modifiers_render": true, "use_subsurf": false, "use_tspace": false}, "include": {"use_custom_props": false}, "transform": {"apply_scale_options": "FBX_SCALE_NONE", "apply_unit_scale": true, "axis_forward": "Y", "axis_up": "Z", "bake_space_transform": false, "global_scale": 1.0}}}}, "disk_animation_folder_path": "C:\\", "disk_groom_folder_path": "C:\\", "disk_mesh_folder_path": "C:\\", "export_all_actions": true, "export_custom_property_fcurves": true, "export_object_name_as_root": true, "extensions": {"affixes": {"animation_sequence_name_affix": "Anim_", "auto_add_asset_name_affixes": false, "auto_remove_asset_name_affixes": false, "material_name_affix": "M_", "show_name_affix_settings": false, "skeletal_mesh_name_affix": "SK_", "static_mesh_name_affix": "SM_", "texture_name_affix": "T_"}, "combine_assets": {"combine": "off"}, "create_post_import_assets_for_groom": {"binding_asset": true, "blueprint_with_groom": false}, "ue2rigify": {"action_prefix": "SOURCE_", "auto_sync_control_nla_to_source": true, "control_mode": "CONTROL", "control_rig_name": "rig", "original_hide_value": true, "use_ue2rigify": false}, "use_collections_as_folders": {"use_collections_as_folders": false}, "use_immediate_parent_name": {"use_immediate_parent_name": false}, "instance_assets": {"place_in_active_level": false, "use_mesh_instances": false}}, "import_animations": true, "import_grooms": true, "import_lods": false, "import_materials_and_textures": true, "import_meshes": true, "lod_regex": "(?i)(_LOD\\d).*", "path_mode": "send_to_project", "tab": "paths", "template_version": 1.0, "unreal": {"editor_skeletal_mesh_library": {"lod_build_settings": {"compute_weighted_normals": true, "morph_threshold_position": 0.014999999664723873, "recompute_normals": false, "recompute_tangents": true, "remove_degenerates": true, "threshold_position": 1.9999999494757503e-05, "threshold_tangent_normal": 1.9999999494757503e-05, "threshold_uv": 0.0009769999887794256, "use_full_precision_u_vs": false, "use_high_precision_tangent_basis": false, "use_mikk_t_space": false}}, "editor_static_mesh_library": {"lod_build_settings": {"build_reversed_index_buffer": true, "build_scale3d": [1.0, 1.0, 1.0], "compute_weighted_normals": true, "distance_field_replacement_mesh": "", "distance_field_resolution_scale": 1.0, "dst_lightmap_index": 1, "generate_distance_field_as_if_two_sided": false, "generate_lightmap_u_vs": false, "min_lightmap_resolution": 64, "recompute_normals": false, "recompute_tangents": false, "remove_degenerates": true, "src_lightmap_index": 0, "use_full_precision_u_vs": false, "use_high_precision_tangent_basis": false, "use_mikk_t_space": false}}, "import_method": {"abc": {"conversion_settings": {"rotation": [90.0, 0.0, 0.0], "scale": [1.0, -1.0, 1.0]}}, "fbx": {"anim_sequence_import_data": {"animation_length": "unreal.FBXAnimationLengthImportType.FBXALIT_EXPORTED_TIME", "convert_scene": false, "convert_scene_unit": false, "custom_sample_rate": 0, "delete_existing_custom_attribute_curves": true, "delete_existing_morph_target_curves": false, "do_not_import_curve_with_zero": true, "force_front_x_axis": false, "frame_import_range": [0, 0], "import_bone_tracks": true, "import_custom_attribute": true, "import_meshes_in_bone_hierarchy": true, "import_rotation": [0.0, 0.0, 0.0], "import_translation": [0.0, 0.0, 0.0], "material_curve_suffixes": "", "preserve_local_transform": true, "remove_redundant_keys": true, "set_material_drive_parameter_on_custom_attribute": false, "use_default_sample_rate": false}, "reset_to_fbx_on_material_conflict": false, "skeletal_mesh_import_data": {"bake_pivot_in_vertex": false, "compute_weighted_normals": true, "convert_scene": false, "convert_scene_unit": false, "force_front_x_axis": false, "import_content_type": "unreal.FBXImportContentType.FBXICT_ALL", "import_meshes_in_bone_hierarchy": true, "import_morph_targets": true, "import_rotation": [0.0, 0.0, 0.0], "import_translation": [0.0, 0.0, 0.0], "morph_threshold_position": 0.014999999664723873, "normal_generation_method": "unreal.FBXNormalGenerationMethod.MIKK_T_SPACE", "normal_import_method": "unreal.FBXNormalImportMethod.FBXNIM_IMPORT_NORMALS_AND_TANGENTS", "preserve_smoothing_groups": true, "reorder_material_to_fbx_order": true, "threshold_position": 1.9999999494757503e-05, "threshold_tangent_normal": 1.9999999494757503e-05, "threshold_uv": 0.0009769999887794256, "transform_vertex_to_absolute": true, "update_skeleton_reference_pose": false, "use_t0_as_ref_pose": false, "vertex_color_import_option": "unreal.VertexColorImportOption.REPLACE", "vertex_override_color": [0.0, 0.0, 0.0, 0.0]}, "static_mesh_import_data": {"auto_generate_collision": true, "bake_pivot_in_vertex": false, "build_nanite": false, "build_reversed_index_buffer": true, "combine_meshes": false, "compute_weighted_normals": true, "convert_scene": false, "convert_scene_unit": false, "force_front_x_axis": false, "generate_lightmap_u_vs": false, "import_rotation": [0.0, 0.0, 0.0], "import_translation": [0.0, 0.0, 0.0], "normal_generation_method": "unreal.FBXNormalGenerationMethod.MIKK_T_SPACE", "normal_import_method": "unreal.FBXNormalImportMethod.FBXNIM_IMPORT_NORMALS_AND_TANGENTS", "one_convex_hull_per_ucx": true, "remove_degenerates": true, "reorder_material_to_fbx_order": true, "static_mesh_lod_group": "none", "transform_vertex_to_absolute": true, "vertex_color_import_option": "unreal.VertexColorImportOption.REPLACE", "vertex_override_color": [0.0, 0.0, 0.0, 0.0]}, "texture_import_data": {"base_color_name": "", "base_diffuse_texture_name": "", "base_emissive_color_name": "", "base_emmisive_texture_name": "", "base_material_name": "", "base_normal_texture_name": "", "base_opacity_texture_name": "", "base_specular_texture_name": "", "convert_scene": false, "convert_scene_unit": false, "force_front_x_axis": false, "import_rotation": [0.0, 0.0, 0.0], "import_translation": [0.0, 0.0, 0.0], "invert_normal_maps": false, "material_search_location": "unreal.MaterialSearchLocation.LOCAL"}}}}, "unreal_animation_folder_path": "/Game/untitled_category/untitled_asset/animations/", "unreal_groom_folder_path": "/Game/untitled_category/untitled_asset/groom/", "unreal_mesh_folder_path": "/Game/untitled_category/untitled_asset/", "unreal_physics_asset_path": "", "unreal_skeletal_mesh_lod_settings_path": "", "unreal_skeleton_asset_path": "", "use_object_origin": false, "validate_armature_transforms": true, "validate_materials": false, "validate_object_names": true, "validate_paths": true, "validate_project_settings": true, "validate_scene_scale": true, "validate_textures": false, "validate_time_units": "off", "validate_unreal_plugins": true}